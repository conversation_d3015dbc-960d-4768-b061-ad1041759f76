import { useTranslation as useI18nTranslation } from 'react-i18next';

/**
 * Custom hook that wraps react-i18next's useTranslation hook
 * Provides type-safe access to translations with better developer experience
 */
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  return {
    t,
    i18n,
    // Helper functions for common translation patterns
    changeLanguage: (lng: string) => i18n.changeLanguage(lng),
    currentLanguage: i18n.language,
    isLoading: !i18n.isInitialized,
  };
};

// Type-safe translation keys for better IDE support
export type TranslationKey =
  // Header translations
  | 'header.navigation.about'
  | 'header.navigation.thinkProtocol'
  | 'header.navigation.resources'
  | 'header.navigation.contact'
  | 'header.dropdown.about.team'
  | 'header.dropdown.about.partners'
  | 'header.dropdown.about.investors'
  | 'header.dropdown.thinkProtocol.thinkToken'
  | 'header.dropdown.thinkProtocol.thinkBuilders'
  | 'header.dropdown.resources.docs'
  | 'header.dropdown.resources.whitepaper'
  | 'header.dropdown.resources.faqs'
  | 'header.buttons.mintAnnouncement'
  | 'header.buttons.joinTheMovement'
  | 'header.buttons.claimThink'
  | 'header.countdown.foundersBonus'
  | 'header.footer.privacy'
  | 'header.footer.terms'
  | 'header.accessibility.openMainMenu'
  | 'header.accessibility.closeMenu'
  | 'header.socialMedia.twitter'
  | 'header.socialMedia.discord'
  | 'header.socialMedia.youtube'
  | 'header.socialMedia.magicEden'
  // Home page translations
  | 'pages.home.hero.title'
  | 'pages.home.hero.subtitle'
  | 'pages.home.features.title'
  | 'pages.home.features.subtitle'
  | 'pages.home.features.description'
  | 'pages.home.features.professionalAgents.title'
  | 'pages.home.features.professionalAgents.description'
  | 'pages.home.features.ownExperience.title'
  | 'pages.home.features.ownExperience.description'
  | 'pages.home.features.patentProtection.title'
  | 'pages.home.features.patentProtection.description'
  | 'pages.home.features.seamlessInteroperability.title'
  | 'pages.home.features.seamlessInteroperability.description'
  // About page translations
  | 'pages.about.title'
  | 'pages.about.hero.title'
  | 'pages.about.hero.subtitle'
  | 'pages.about.hero.mission'
  | 'pages.about.description.paragraph1'
  | 'pages.about.description.paragraph2'
  | 'pages.about.description.paragraph3'
  | 'pages.about.team.title'
  // Contact page translations
  | 'pages.contact.title'
  | 'pages.contact.subtitle'
  | 'pages.contact.form.fullName'
  | 'pages.contact.form.email'
  | 'pages.contact.form.twitterHandle'
  | 'pages.contact.form.message'
  | 'pages.contact.form.submit'
  | 'pages.contact.form.submitting'
  | 'pages.contact.errors.generic'
  // Dashboard translations
  | 'pages.dashboard.greeting'
  | 'pages.dashboard.greetingDefault'
  | 'pages.dashboard.yourRewards'
  | 'pages.dashboard.availableToStake'
  | 'pages.dashboard.claimAndRestake'
  | 'pages.dashboard.ineligible'
  | 'pages.dashboard.notImplemented'
  // Thinkubator translations
  | 'pages.thinkubator.hero.title'
  | 'pages.thinkubator.hero.season'
  | 'pages.thinkubator.hero.seasonTime'
  | 'pages.thinkubator.about.description'
  | 'pages.thinkubator.errors.notEnabled'
  // Common translations
  | 'common.buttons.applyNow'
  | 'common.buttons.learnMore'
  | 'common.buttons.continue'
  | 'common.buttons.submit'
  | 'common.buttons.cancel'
  | 'common.buttons.close'
  | 'common.buttons.save'
  | 'common.buttons.edit'
  | 'common.buttons.delete'
  | 'common.buttons.back'
  | 'common.buttons.next'
  | 'common.loading'
  | 'common.error'
  | 'common.success'
  | 'common.warning'
  | 'common.info';

export default useTranslation;
